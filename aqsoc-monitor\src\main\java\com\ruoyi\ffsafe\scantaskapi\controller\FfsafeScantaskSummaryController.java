package com.ruoyi.ffsafe.scantaskapi.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MinioUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummary;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryDetailVO;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryQueryParam;
import com.ruoyi.ffsafe.scantaskapi.domain.BatchCreateReportParam;
import com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/hostscan/tasksummary")
public class FfsafeScantaskSummaryController extends BaseController {
    @Autowired
    private IFfsafeScantaskSummaryService ffsafeScantaskSummaryService;

    @Autowired
    private ScanTaskReport scanTaskReport;

    @Autowired
    private MinioUtil minioUtil;

    /**
     * 查询非凡扫描任务汇总列表
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:list')")
    @GetMapping("/list")
    public TableDataInfo list(FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        startPage();
        List<FfsafeScantaskSummary> list = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
        return getDataTable(list);
    }

    /**
     * 导出非凡扫描任务汇总列表
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:export')")
    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        List<FfsafeScantaskSummary> list = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
        ExcelUtil<FfsafeScantaskSummary> util = new ExcelUtil<FfsafeScantaskSummary>(FfsafeScantaskSummary.class);
        util.exportExcel(response, list, "非凡扫描任务汇总数据");
    }

    /**
     * 获取非凡扫描任务汇总详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryById(id));
    }

    /**
     * 新增非凡扫描任务汇总
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:add')")
    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        return toAjax(ffsafeScantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary));
    }

    /**
     * 修改非凡扫描任务汇总
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:edit')")
    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        return toAjax(ffsafeScantaskSummaryService.updateFfsafeScantaskSummary(ffsafeScantaskSummary));
    }

    /**
     * 删除非凡扫描任务汇总
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:remove')")
    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ffsafeScantaskSummaryService.deleteFfsafeScantaskSummaryByIds(ids));
    }

    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.OTHER)
    @PostMapping("/createreport")
    public AjaxResult createReport(@RequestBody FfsafeScantaskSummary ffsafeScantaskSummary) {
        String fileName = "" + ffsafeScantaskSummary.getTaskId() + "_" + ffsafeScantaskSummary.getTaskType() + "_" + DateUtils.dateTimeNow();
        try {
            boolean bRet = scanTaskReport.createTaskReport(ffsafeScantaskSummary.getTaskId(), ffsafeScantaskSummary.getTaskType(), fileName);
            if (bRet) {
                return AjaxResult.success("报告生成中。待报告生成后进行下载!");
            }
            return AjaxResult.error("数据库错误。");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.OTHER)
    @PostMapping("/downreport")
    public AjaxResult downReport(@RequestBody FfsafeScantaskSummary ffsafeScantaskSummary) {
        if (ffsafeScantaskSummary.getReportStatus() != 2) {
            return AjaxResult.error("报告生成中， 请稍后下载!");
        }

        String fileName = ffsafeScantaskSummary.getFileName() + ".zip";
        String minioFileUrl = minioUtil.getPath(fileName);
        if (minioFileUrl == null) {
            return AjaxResult.error("下载报表出错， 请联系管理员.");
        }
        return AjaxResult.success(minioFileUrl);
    }

    /**
     * 批量生成扫描任务报告
     */
    @Log(title = "批量生成漏扫报告", businessType = BusinessType.OTHER)
    @PostMapping("/createBatchTaskReport")
    public AjaxResult createBatchTaskReport(@RequestBody BatchCreateReportParam batchReportParam) {
        try {
            // 1. 参数验证
            if (batchReportParam.getIds() == null || batchReportParam.getIds().length == 0) {
                return AjaxResult.error("扫描任务汇总ID列表不能为空");
            }

            if (batchReportParam.getTaskType() == null) {
                return AjaxResult.error("任务类型不能为空");
            }

            // 2. 生成批量报告文件名
            String fileName = generateBatchReportFileName(batchReportParam.getIds(),
                                                        batchReportParam.getTaskType(),
                                                        batchReportParam.getFileName());

            // 3. 调用批量报告生成核心逻辑
            boolean success = scanTaskReport.createBatchTaskReport(batchReportParam.getIds(),
                                                                 batchReportParam.getTaskType(),
                                                                 fileName);

            if (success) {
                return AjaxResult.success("报告生成中,待报告生成后进行下载!");
            }
            return AjaxResult.error("报告生成失败，请检查任务状态。");

        } catch (Exception e) {
            logger.error("生成报告时发生异常", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 生成批量报告文件名
     * @param summaryIds 扫描任务汇总ID数组
     * @param taskType 任务类型
     * @param customFileName 自定义文件名（可选）
     * @return 生成的文件名
     */
    private String generateBatchReportFileName(Long[] summaryIds, Integer taskType, String customFileName) {
        // 如果提供了自定义文件名，则使用自定义文件名
        if (customFileName != null && !customFileName.trim().isEmpty()) {
            return customFileName.trim() + "_" + DateUtils.dateTimeNow();
        }

        // 否则生成默认的批量文件名
        String taskTypeStr = (taskType == 1) ? "web" : "host";
        String summaryIdRange = summaryIds.length == 1 ?
                               String.valueOf(summaryIds[0]) :
                               summaryIds[0] + "-" + summaryIds[summaryIds.length - 1];

        return "batch_" + taskTypeStr + "_" + summaryIdRange + "_" + DateUtils.dateTimeNow();
    }

    /**
     * 查询主机漏扫记录详细信息列表
     * 支持任务名称和扫描目标的模糊查询，返回包含统计信息的详细数据
     */
    @GetMapping("/listWithDetails")
    public TableDataInfo listWithDetails(FfsafeScantaskSummaryQueryParam queryParam)
    {
        startPage();
        List<FfsafeScantaskSummaryDetailVO> list = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryDetailList(queryParam);
        return getDataTable(list);
    }
}
