package com.ruoyi.ffsafe.scantaskapi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 扫描报告记录对象 ffsafe_scan_report_record
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
public class FfsafeScanReportRecord extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 扫描目标，单条时为单个目标，批量时用分号分隔多个目标 */
    @Excel(name = "扫描目标")
    private String scanTarget;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date generateTime;

    /** 生成入口：1=单条生成，2=批量生成 */
    @Excel(name = "生成入口", readConverterExp = "1=单条生成,2=批量生成")
    private Integer generateSource;

    /** 报表类型 2: 主机漏扫报表  1: web漏扫报表 */
    @Excel(name = "报表类型", readConverterExp = "1=web漏扫报表,2=主机漏扫报表")
    private Integer reportType;

    /** 报表ID */
    @Excel(name = "报表ID")
    private Integer reportId;

    /** 报表状态: 0: 生成中  1: 生成完毕  2: 下载完毕 */
    @Excel(name = "报表状态", readConverterExp = "0=生成中,1=生成完毕,2=下载完毕")
    private Integer reportStatus;

    /** 报表文件名 */
    @Excel(name = "报表文件名")
    private String fileName;

    /** 报表下载名 */
    @Excel(name = "报表下载名")
    private String downName;

    /** 报表进度 0-100 */
    @Excel(name = "报表进度")
    private Integer reportPercent;

    /** minio 路径 */
    @Excel(name = "minio路径")
    private String minioPath;

    // 常量定义
    /** 生成入口：单条生成 */
    public static final Integer GENERATE_SOURCE_SINGLE = 1;

    /** 生成入口：批量生成 */
    public static final Integer GENERATE_SOURCE_BATCH = 2;
    
    /** 报表状态：生成中 */
    public static final Integer REPORT_STATUS_GENERATING = 0;
    
    /** 报表状态：生成完毕 */
    public static final Integer REPORT_STATUS_COMPLETED = 1;
    
    /** 报表状态：下载完毕 */
    public static final Integer REPORT_STATUS_DOWNLOADED = 2;
    
    /** 报表类型：web漏扫报表 */
    public static final Integer REPORT_TYPE_WEB = 1;
    
    /** 报表类型：主机漏扫报表 */
    public static final Integer REPORT_TYPE_HOST = 2;
}
