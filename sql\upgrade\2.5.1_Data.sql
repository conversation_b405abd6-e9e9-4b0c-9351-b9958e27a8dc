use aqsoc;

INSERT INTO `aqsoc`.`sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (231, '是否开放公网', 'is_open_network', '0', 'admin', '2025-08-06 17:17:31', '', NULL, NULL);
INSERT INTO `aqsoc`.`sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (232, 'HW时期是否可关停', 'hw_is_true_shut_down', '0', 'admin', '2025-08-06 17:18:53', '', NULL, NULL);
INSERT INTO `aqsoc`.`sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (233, '是否热设备', 'is_sparing', '0', 'admin', '2025-08-06 17:21:00', '', NULL, NULL);


INSERT INTO `aqsoc`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (865, 1, '否', '0', 'is_open_network', NULL, 'default', 'N', '0', 'admin', '2025-08-06 17:18:05', '', NULL, NULL);
INSERT INTO `aqsoc`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (866, 2, '是', '1', 'is_open_network', NULL, 'default', 'N', '0', 'admin', '2025-08-06 17:18:19', '', NULL, NULL);
INSERT INTO `aqsoc`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (867, 1, '否', '0', 'hw_is_true_shut_down', NULL, 'default', 'N', '0', 'admin', '2025-08-06 17:19:11', '', NULL, NULL);
INSERT INTO `aqsoc`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (868, 2, '是', '1', 'hw_is_true_shut_down', NULL, 'default', 'N', '0', 'admin', '2025-08-06 17:20:03', '', NULL, NULL);
INSERT INTO `aqsoc`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (869, 0, '否', '0', 'is_sparing', NULL, 'default', 'N', '0', 'admin', '2025-08-06 17:21:35', '', NULL, NULL);
INSERT INTO `aqsoc`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (870, 1, '是', '1', 'is_sparing', NULL, 'default', 'N', '0', 'admin', '2025-08-06 17:21:47', '', NULL, NULL);


-- 1. 插入字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES
('APP平台类型', 'app_platform_type', '0', 'admin', NOW(), 'APP应用程序的平台类型字典'),
('公众号账号类型', 'official_account_type', '0', 'admin', NOW(), '微信公众号的账号类型字典'),
('小程序类型', 'mini_program_type', '0', 'admin', NOW(), '微信小程序的类型字典');

-- 2. 插入字典数据 - APP平台类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, 'iOS', 'ios', 'app_platform_type', '', 'primary', 'N', '0', 'admin', NOW(), 'iOS平台应用'),
(2, 'Android', 'android', 'app_platform_type', '', 'success', 'N', '0', 'admin', NOW(), 'Android平台应用'),
(3, '鸿蒙', 'harmonyos', 'app_platform_type', '', 'info', 'N', '0', 'admin', NOW(), '华为鸿蒙系统应用'),
(4, 'Windows', 'windows', 'app_platform_type', '', 'warning', 'N', '0', 'admin', NOW(), 'Windows平台应用'),
(5, 'macOS', 'macos', 'app_platform_type', '', 'default', 'N', '0', 'admin', NOW(), 'macOS平台应用'),
(6, 'Linux', 'linux', 'app_platform_type', '', 'default', 'N', '0', 'admin', NOW(), 'Linux平台应用'),
(7, '跨平台', 'cross_platform', 'app_platform_type', '', 'info', 'N', '0', 'admin', NOW(), '跨平台应用');

-- 3. 插入字典数据 - 公众号账号类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '订阅号', 'subscription', 'official_account_type', '', 'primary', 'N', '0', 'admin', NOW(), '微信订阅号'),
(2, '服务号', 'service', 'official_account_type', '', 'success', 'N', '0', 'admin', NOW(), '微信服务号'),
(3, '企业号', 'enterprise', 'official_account_type', '', 'info', 'N', '0', 'admin', NOW(), '微信企业号'),
(4, '小程序', 'mini_program', 'official_account_type', '', 'warning', 'N', '0', 'admin', NOW(), '微信小程序');

-- 4. 插入字典数据 - 小程序类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '工具类', 'tool', 'mini_program_type', '', 'primary', 'N', '0', 'admin', NOW(), '工具类小程序'),
(2, '生活服务', 'life_service', 'mini_program_type', '', 'success', 'N', '0', 'admin', NOW(), '生活服务类小程序'),
(3, '电商购物', 'ecommerce', 'mini_program_type', '', 'warning', 'N', '0', 'admin', NOW(), '电商购物类小程序'),
(4, '游戏娱乐', 'game_entertainment', 'mini_program_type', '', 'info', 'N', '0', 'admin', NOW(), '游戏娱乐类小程序'),
(5, '教育培训', 'education', 'mini_program_type', '', 'default', 'N', '0', 'admin', NOW(), '教育培训类小程序'),
(6, '医疗健康', 'healthcare', 'mini_program_type', '', 'success', 'N', '0', 'admin', NOW(), '医疗健康类小程序'),
(7, '金融理财', 'finance', 'mini_program_type', '', 'danger', 'N', '0', 'admin', NOW(), '金融理财类小程序'),
(8, '政务服务', 'government', 'mini_program_type', '', 'info', 'N', '0', 'admin', NOW(), '政务服务类小程序'),
(9, '企业应用', 'enterprise', 'mini_program_type', '', 'primary', 'N', '0', 'admin', NOW(), '企业应用类小程序'),
(10, '其他', 'other', 'mini_program_type', '', 'default', 'N', '0', 'admin', NOW(), '其他类型小程序');

INSERT INTO `threaten_strategy_class` (`id`, `strategy_name`, `strategy_class`, `create_time`, `params_define`) VALUES (102, '非凡蜜罐攻击策略', 'cn.anmte.aqsoc.threaten.strategy.plugin.FfHoneypotTableStrategy', '2025-08-12 20:02:06', '[]');
