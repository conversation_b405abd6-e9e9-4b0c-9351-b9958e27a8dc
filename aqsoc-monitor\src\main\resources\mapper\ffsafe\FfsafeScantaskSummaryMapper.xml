<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeScantaskSummaryMapper">

    <resultMap type="FfsafeScantaskSummary" id="FfsafeScantaskSummaryResult">
        <result property="id"    column="id"    />
        <result property="jobId"    column="job_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskType"    column="task_type"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="finishRate"    column="finish_rate"    />
        <result property="highRiskNum"    column="high_risk_num"    />
        <result property="middleRiskNum"    column="middle_risk_num"    />
        <result property="lowRiskNum"    column="low_risk_num"    />
        <result property="pocRiskNum"    column="poc_risk_num"    />
        <result property="infoRiskNum"    column="info_risk_num"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="reportId"    column="report_id"    />
        <result property="reportStatus"    column="report_status"    />
        <result property="fileName"    column="file_name"    />
        <result property="downName"    column="down_name"    />
        <result property="reportPercent"    column="report_percent"    />
        <result property="minioPath"    column="minio_path"    />
    </resultMap>

    <resultMap type="FfsafeScantaskSummaryDetailVO" id="FfsafeScantaskSummaryDetailResult" extends="FfsafeScantaskSummaryResult">
        <result property="jobName"    column="job_name"    />
        <result property="scanTarget"    column="scan_target"    />
        <result property="scanTargetRaw"    column="scan_target_raw"    />
        <result property="hostNum"    column="host_num"    />
        <result property="pwNum"    column="pw_num"    />
    </resultMap>

    <sql id="selectFfsafeScantaskSummaryVo">
        select 
            s.id, s.job_id, s.task_id, s.task_type, s.task_status, s.finish_rate, 
            s.high_risk_num, s.middle_risk_num, s.low_risk_num, s.poc_risk_num, s.info_risk_num, 
            s.start_time, s.end_time,
            r.report_id,
            r.report_status,
            r.file_name,
            r.down_name,
            r.report_percent,
            r.minio_path
        from ffsafe_scantask_summary s
        left join ffsafe_scan_report_task_relation rel on s.id = rel.task_summary_id
        left join ffsafe_scan_report_record r on rel.scan_report_record_id = r.id
    </sql>

    <select id="selectFfsafeScantaskSummaryList" parameterType="FfsafeScantaskSummary" resultMap="FfsafeScantaskSummaryResult">
        <include refid="selectFfsafeScantaskSummaryVo"/>
        <where>
            <if test="jobId != null "> and s.job_id = #{jobId}</if>
            <if test="taskId != null "> and s.task_id = #{taskId}</if>
            <if test="taskType != null "> and s.task_type = #{taskType}</if>
            <if test="taskStatus != null "> and s.task_status = #{taskStatus}</if>
            <if test="finishRate != null "> and s.finish_rate = #{finishRate}</if>
            <if test="highRiskNum != null "> and s.high_risk_num = #{highRiskNum}</if>
            <if test="middleRiskNum != null "> and s.middle_risk_num = #{middleRiskNum}</if>
            <if test="lowRiskNum != null "> and s.low_risk_num = #{lowRiskNum}</if>
            <if test="pocRiskNum != null "> and s.poc_risk_num = #{pocRiskNum}</if>
            <if test="infoRiskNum != null "> and s.info_risk_num = #{infoRiskNum}</if>
            <if test="startTime != null "> and s.start_time = #{startTime}</if>
            <if test="endTime != null "> and s.end_time = #{endTime}</if>
            <if test="reportId != null "> and r.report_id = #{reportId}</if>
            <if test="reportStatus != null "> and r.report_status = #{reportStatus}</if>
            <if test="fileName != null "> and r.file_name = #{fileName}</if>
            <if test="downName != null "> and r.down_name = #{downName}</if>
            <if test="reportPercent != null "> and r.report_percent = #{reportPercent}</if>
            <if test="minioPath != null "> and r.minio_path = #{minioPath}</if>
        </where>
    </select>

    <select id="selectFfsafeScantaskSummaryById" parameterType="Long" resultMap="FfsafeScantaskSummaryResult">
        <include refid="selectFfsafeScantaskSummaryVo"/>
        where s.id = #{id}
    </select>

    <select id="selectFfsafeScantaskSummaryByIds" parameterType="Long" resultMap="FfsafeScantaskSummaryResult">
        <include refid="selectFfsafeScantaskSummaryVo"/>
        where s.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeScantaskSummary" parameterType="FfsafeScantaskSummary" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_scantask_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobId != null">job_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskType != null">task_type,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="finishRate != null">finish_rate,</if>
            <if test="highRiskNum != null">high_risk_num,</if>
            <if test="middleRiskNum != null">middle_risk_num,</if>
            <if test="lowRiskNum != null">low_risk_num,</if>
            <if test="pocRiskNum != null">poc_risk_num,</if>
            <if test="infoRiskNum != null">info_risk_num,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="reportId != null">report_id,</if>
            <if test="reportStatus != null">report_status,</if>
            <if test="fileName != null">file_name,</if>
            <if test="downName != null ">down_name,</if>
            <if test="reportPercent != null">report_percent,</if>
            <if test="minioPath != null">minio_path,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobId != null">#{jobId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="finishRate != null">#{finishRate},</if>
            <if test="highRiskNum != null">#{highRiskNum},</if>
            <if test="middleRiskNum != null">#{middleRiskNum},</if>
            <if test="lowRiskNum != null">#{lowRiskNum},</if>
            <if test="pocRiskNum != null">#{pocRiskNum},</if>
            <if test="infoRiskNum != null">#{infoRiskNum},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="reportId != null">#{reportId},</if>
            <if test="reportStatus != null">#{reportStatus},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="downName != null">#{downName},</if>
            <if test="reportPercent != null">#{reportPercent},</if>
            <if test="minioPath != null">#{minioPath},</if>
        </trim>
    </insert>

    <update id="updateFfsafeScantaskSummary" parameterType="FfsafeScantaskSummary">
        update ffsafe_scantask_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="finishRate != null">finish_rate = #{finishRate},</if>
            <if test="highRiskNum != null">high_risk_num = #{highRiskNum},</if>
            <if test="middleRiskNum != null">middle_risk_num = #{middleRiskNum},</if>
            <if test="lowRiskNum != null">low_risk_num = #{lowRiskNum},</if>
            <if test="pocRiskNum != null">poc_risk_num = #{pocRiskNum},</if>
            <if test="infoRiskNum != null">info_risk_num = #{infoRiskNum},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="reportId != null">report_id = #{reportId},</if>
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="downName != null">down_name = #{downName},</if>
            <if test="reportPercent != null">report_percent = #{reportPercent},</if>
            <if test="minioPath != null">minio_path = #{minioPath},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateFfsafeScantaskSummaryByTaskId" parameterType="FfsafeScantaskSummary">
        update ffsafe_scantask_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="finishRate != null">finish_rate = #{finishRate},</if>
            <if test="highRiskNum != null">high_risk_num = #{highRiskNum},</if>
            <if test="middleRiskNum != null">middle_risk_num = #{middleRiskNum},</if>
            <if test="lowRiskNum != null">low_risk_num = #{lowRiskNum},</if>
            <if test="pocRiskNum != null">poc_risk_num = #{pocRiskNum},</if>
            <if test="infoRiskNum != null">info_risk_num = #{infoRiskNum},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="reportId != null">report_id = #{reportId},</if>
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="downName != null">down_name = #{downName},</if>
            <if test="reportPercent != null">report_percent = #{reportPercent},</if>
            <if test="minioPath != null">minio_path = #{minioPath},</if>
        </trim>
        where task_id = #{taskId} and task_type = #{taskType}
    </update>

    <delete id="deleteFfsafeScantaskSummaryById" parameterType="Long">
        delete from ffsafe_scantask_summary where id = #{id}
    </delete>

    <delete id="deleteFfsafeScantaskSummaryByIds" parameterType="String">
        delete from ffsafe_scantask_summary where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFfsafeScantaskSummaryDetailList" parameterType="FfsafeScantaskSummaryQueryParam" resultMap="FfsafeScantaskSummaryDetailResult">
        SELECT
            s.id,
            s.job_id,
            s.task_id,
            s.task_type,
            s.task_status,
            s.finish_rate,
            s.high_risk_num,
            s.middle_risk_num,
            s.low_risk_num,
            s.poc_risk_num,
            s.info_risk_num,
            s.start_time,
            s.end_time,
            r.report_id,
            r.report_status,
            r.file_name,
            r.down_name,
            r.report_percent,
            r.minio_path,
            j.job_name,
            j.invoke_target as scan_target_raw,
            '' as scan_target,
            COALESCE(h.alive_count, 0) as host_num,
            COALESCE(w.weak_password_count, 0) as pw_num
        FROM ffsafe_scantask_summary s
        LEFT JOIN sys_job j ON s.job_id = j.job_id
        LEFT JOIN ffsafe_scan_report_task_relation rel ON s.id = rel.task_summary_id
        LEFT JOIN ffsafe_scan_report_record r ON rel.scan_report_record_id = r.id
        LEFT JOIN (
            SELECT task_id, COUNT(*) as alive_count
            FROM ffsafe_hostscan_taskresult
            GROUP BY task_id
        ) h ON s.task_id = h.task_id
        LEFT JOIN (
            SELECT task_id, COUNT(*) as weak_password_count
            FROM ffsafe_hostscan_wpresult
            GROUP BY task_id
        ) w ON s.task_id = w.task_id
        <where>
            <if test="jobId != null"> and s.job_id = #{jobId}</if>
            <if test="taskType != null"> and s.task_type = #{taskType}</if>
            <if test="taskStatus != null"> and s.task_status = #{taskStatus}</if>
            <if test="jobName != null and jobName != ''"> and j.job_name LIKE CONCAT('%', #{jobName}, '%')</if>
            <if test="scanTarget != null and scanTarget != ''"> and j.invoke_target LIKE CONCAT('%', #{scanTarget}, '%')</if>
        </where>
        ORDER BY s.id DESC
    </select>
</mapper>