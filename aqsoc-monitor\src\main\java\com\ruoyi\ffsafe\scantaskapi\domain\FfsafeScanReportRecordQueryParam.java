package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 扫描报告记录查询参数类
 * 用于分页查询扫描报告记录，支持按报告类型和扫描目标进行筛选
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
public class FfsafeScanReportRecordQueryParam extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 报告类型 - 精确匹配查询
     * 1: web漏扫报表
     * 2: 主机漏扫报表
     * 为空时不筛选此条件
     */
    private Integer reportType;

    /** 扫描目标 - 支持模糊查询
     * 可以是单个目标或多个目标（分号分隔）
     * 为空时不筛选此条件
     */
    private String scanTarget;

    /** 报表状态 - 精确匹配查询（可选扩展字段）
     * 0: 生成中
     * 1: 生成完毕
     * 2: 下载完毕
     * 为空时不筛选此条件
     */
    private Integer reportStatus;

    /** 生成入口 - 精确匹配查询（可选扩展字段）
     * 1: 单条生成
     * 2: 批量生成
     * 为空时不筛选此条件
     */
    private Integer generateSource;
}
