package com.ruoyi.ffsafe.scantaskapi.event;

import com.ruoyi.ffsafe.scantaskapi.domain.CreateTaskReportParam;
import com.ruoyi.ffsafe.scantaskapi.domain.CreateTaskReportResult;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummary;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecord;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecordWithTaskInfo;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScanReportRecordService;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScanReportTaskRelationService;
import com.ruoyi.ffsafe.scantaskapi.service.IScanTaskService;
import com.ruoyi.ffsafe.scantaskapi.utils.ScanTargetUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.service.ISysJobService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Data
@Component
public class ScanTaskReport {
    @Autowired
    private IScanTaskService scanTaskService;
    @Autowired
    private IFfsafeScantaskSummaryService ffsafeScantaskSummaryService;
    @Autowired
    private IFfsafeScanReportRecordService ffsafeScanReportRecordService;
    @Autowired
    private IFfsafeScanReportTaskRelationService ffsafeScanReportTaskRelationService;
    @Autowired
    private ISysJobService sysJobService;
    @Autowired
    private CreateTaskReportParam createTaskReportParam;
    @Autowired
    private TaskReportMonitorEvent taskReportMonitorEvent;
    @Autowired
    private BatchReportMonitorEvent batchReportMonitorEvent;

    private FfsafeScantaskSummary getTaskSummary(int taskId, int taskType) {
        FfsafeScantaskSummary ffsafeScantaskSummary = new FfsafeScantaskSummary();
        ffsafeScantaskSummary.setTaskId(taskId);
        ffsafeScantaskSummary.setTaskType(taskType);
        List<FfsafeScantaskSummary> ffsafeScantaskSummaryList = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
        if ((ffsafeScantaskSummaryList != null) &&(ffsafeScantaskSummaryList.size() > 0)) {
            return ffsafeScantaskSummaryList.get(0);
        }

        return null;
    }

    public boolean createTaskReport(int taskId, int taskType, String fileName) throws Exception {
        FfsafeScantaskSummary ffsafeScantaskSummary = getTaskSummary(taskId, taskType);
        if (ffsafeScantaskSummary == null) {
            throw new Exception("当前任务不存在!");
        }
        if (ffsafeScantaskSummary.getReportId() != null) {
            if (ffsafeScantaskSummary.getReportStatus() == 0) {
                throw new Exception("当前任务报表生成中，请勿重复生成!");
            }
            if (ffsafeScantaskSummary.getReportStatus() == 1) {    // 后台已生成， 但未上传minio
                throw new Exception("当前任务报表生成中，请勿重复生成!");
            }
            if (ffsafeScantaskSummary.getReportStatus() == 2) {
                throw new Exception("当前任务报表生成完毕。 请直接下载!");
            }
        }
        int reportType = 0;
        if (taskType == 1) { reportType = 2; }
        if (taskType == 2) { reportType = 1; }

        createTaskReportParam.parseParam(taskId, reportType, fileName);
        CreateTaskReportResult createTaskReportResult = scanTaskService.createTaskReport(createTaskReportParam);
        if (createTaskReportResult == null) {
            log.warn("生成报表失败: taskId: " + taskId + " taskType: " + taskType);
            return false;
        }
        FfsafeScantaskSummary tempSummary = new FfsafeScantaskSummary();
        tempSummary.setId(ffsafeScantaskSummary.getId());
        tempSummary.setReportId(createTaskReportResult.getTaskId());
        tempSummary.setTaskType(taskType);
        tempSummary.setReportStatus(0);  // 生成中
        tempSummary.setFileName(fileName);
        int nRet = ffsafeScantaskSummaryService.updateFfsafeScantaskSummary(tempSummary);
        taskReportMonitorEvent.addTaskReport(tempSummary);
        return nRet>0;
    }

    /**
     * 批量创建任务报告
     * @param summaryIds 扫描任务汇总主键ID数组
     * @param taskType 任务类型
     * @param fileName 文件名
     * @return 是否成功
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createBatchTaskReport(Long[] summaryIds, int taskType, String fileName) throws Exception {
        try {
            log.info("开始生成报告，任务汇总ID数量: {}, 任务类型: {}, 文件名: {}",
                    summaryIds != null ? summaryIds.length : 0, taskType, fileName);

            // 1. 参数验证
            if (summaryIds == null || summaryIds.length == 0) {
                log.warn("生成报告失败：任务汇总ID列表为空");
                throw new ServiceException("请选择漏扫记录");
            }

            // 2. 批量查询任务汇总信息（用于验证任务完成状态）
            log.debug("开始查询任务汇总信息，任务汇总ID: {}", java.util.Arrays.toString(summaryIds));
            List<FfsafeScanReportRecordWithTaskInfo> validatedReports = ffsafeScanReportRecordService.getReportRecordsWithTaskInfoBySummaryIds(summaryIds);
            log.info("成功查询到 {} 条任务汇总记录", validatedReports != null ? validatedReports.size() : 0);

            // 2.1. 验证任务完成状态（finish_rate=100 且 task_status=4）
            validateTaskCompletionStatus(validatedReports);

            // 3. 检查是否有任务已经在生成报告（仅单条记录时检查）
            if (summaryIds.length == 1) {
                log.debug("单条记录模式，检查重复报告生成");
                validateNoDuplicateReports(validatedReports);
            }

            // 4. 构建批量扫描目标信息
            log.debug("开始构建批量扫描目标信息");
            String batchScanTarget = buildBatchScanTarget(validatedReports);
            log.info("批量扫描目标构建完成，目标数量: {}", batchScanTarget.split(";").length);

            // 5. 提取任务ID用于第三方接口调用
            log.debug("开始提取任务ID用于第三方接口调用");
            Integer[] taskIds = extractTaskIds(validatedReports);
            log.info("成功提取 {} 个任务ID: {}", taskIds.length, java.util.Arrays.toString(taskIds));

            // 6. 转换报告类型（与单个任务保持一致）
            int reportType = (taskType == 1) ? 2 : 1;

            // 7. 使用现有的 CreateTaskReportParam，传入批量 taskIds
            Long[] taskIdsLong = Arrays.stream(taskIds).map(Integer::longValue).toArray(Long[]::new);
            createTaskReportParam.parseParam(taskIdsLong, reportType, fileName);

            // 8. 调用第三方接口（复用现有方法）
            CreateTaskReportResult createTaskReportResult = scanTaskService.createTaskReport(createTaskReportParam);

            if (createTaskReportResult == null) {
                log.warn("生成报告失败: taskIds: {}, taskType: {}", Arrays.toString(taskIds), taskType);
                throw new ServiceException("生成报告失败");
            }

            if (!"创建成功".equals(createTaskReportResult.getMessage())) {
                log.warn("批量生成报表失败: taskIds: {}, taskType: {}, 错误信息: {}",
                        Arrays.toString(taskIds), taskType, createTaskReportResult.getMessage());
                throw new ServiceException("生成报告失败: " + createTaskReportResult.getMessage());
            }

            // 9. 创建批量报告记录
            FfsafeScanReportRecord batchRecord = new FfsafeScanReportRecord();
            batchRecord.setScanTarget(batchScanTarget);
            batchRecord.setGenerateSource(FfsafeScanReportRecord.GENERATE_SOURCE_BATCH);
            batchRecord.setReportType(taskType);
            batchRecord.setReportId(createTaskReportResult.getTaskId());
            batchRecord.setReportStatus(FfsafeScanReportRecord.REPORT_STATUS_GENERATING);
            batchRecord.setFileName(fileName);
            // 注意：不再设置 relatedTaskSummaryIds 字段，改为使用关联表存储关联关系
            batchRecord.setReportPercent(0);  // 初始进度为0

            // 10. 插入批量报告记录
            int insertResult = ffsafeScanReportRecordService.insertFfsafeScanReportRecord(batchRecord);

            if (insertResult <= 0) {
                throw new ServiceException("生成报告失败");
            }

            // 10.1 插入关联关系记录（用于优化查询性能）
            insertReportTaskRelations(batchRecord.getId(), summaryIds);

            // 11. 添加到批量报告监控事件
            batchReportMonitorEvent.addBatchReport(batchRecord);

            log.info("生成报告成功，第三方报告ID: {}, 本地记录ID: {}, 扫描目标: {}",
                    createTaskReportResult.getTaskId(), batchRecord.getId(), batchScanTarget);
            return true;

        } catch (ServiceException e) {
            log.error("生成报告业务异常，任务汇总ID: {}, 错误信息: {}",
                    java.util.Arrays.toString(summaryIds), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("生成报告系统异常，任务汇总ID: {}",
                    java.util.Arrays.toString(summaryIds), e);
            throw new ServiceException("生成报告失败: " + e.getMessage());
        }
    }







    /**
     * 验证是否生成过报告（优化版，接收任务汇总列表）
     * @param reports 已验证的任务汇总列表
     * @throws ServiceException 
     */
    private void validateNoDuplicateReports(List<FfsafeScanReportRecordWithTaskInfo> reports) throws ServiceException {
        for (FfsafeScanReportRecordWithTaskInfo report : reports) {
            if (report.getReportId() != null && report.getReportStatus() != null) {
                if (Objects.nonNull(report.getReportStatus())) {
                    log.error("任务汇总ID " + report.getOriginalTaskId() + " 已经生成过报告，请勿重复生成!");
                    throw new ServiceException("已经生成过报告，请勿重复生成!");
                }
            }
        }
    }

    /**
     * 构建批量扫描目标信息（优化版，接收报表记录列表）
     * @param reports 已验证的报表记录列表
     * @return 扫描目标信息，用分号分隔
     */
    private String buildBatchScanTarget(List<FfsafeScanReportRecordWithTaskInfo> reports) {
        List<String> targets = new ArrayList<>();
        for (FfsafeScanReportRecordWithTaskInfo report : reports) {
            try {
                if (report.getJobId() != null) {
                    // 通过 jobId 获取 sys_job 信息
                    SysJob sysJob = sysJobService.selectJobById(report.getJobId().longValue());
                    if (sysJob != null && sysJob.getInvokeTarget() != null) {
                        // 使用工具类解析扫描目标
                        String scanTarget = ScanTargetUtils.extractScanTarget(sysJob.getInvokeTarget());
                        if (!scanTarget.isEmpty()) {
                            targets.add(scanTarget);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("获取报告记录 {} 的扫描目标失败: {}", report.getId(), e.getMessage());
            }
        }
        return String.join(";", targets);
    }

    /**
     * 从报表记录列表中提取任务ID用于第三方接口调用
     * @param reports 报表记录列表
     * @return 任务ID数组
     */
    private Integer[] extractTaskIds(List<FfsafeScanReportRecordWithTaskInfo> reports) {
        return reports.stream()
                     .map(FfsafeScanReportRecordWithTaskInfo::getOriginalTaskId)
                     .toArray(Integer[]::new);
    }

    /**
     * 插入报告与任务的关联关系记录
     * @param reportRecordId 报告记录ID
     * @param summaryIds 任务汇总ID数组
     */
    private void insertReportTaskRelations(Long reportRecordId, Long[] summaryIds) {
        try {
            log.info("开始插入报告与任务的关联关系记录，报告ID: {}, 关联任务数: {}",
                    reportRecordId, summaryIds != null ? summaryIds.length : 0);

            if (reportRecordId == null) {
                log.warn("报告记录ID为空，跳过关联关系插入");
                return;
            }

            if (summaryIds == null || summaryIds.length == 0) {
                log.warn("任务汇总ID列表为空，跳过关联关系插入");
                return;
            }

            // 使用关联表Service批量插入关联关系
            int insertCount = ffsafeScanReportTaskRelationService.batchInsertRelations(reportRecordId, summaryIds);

            if (insertCount > 0) {
                log.info("报告与任务关联关系插入成功，报告ID: {}, 插入记录数: {}", reportRecordId, insertCount);
            } else {
                log.warn("报告与任务关联关系插入失败，报告ID: {}, 任务数: {}", reportRecordId, summaryIds.length);
            }

        } catch (Exception e) {
            log.error("插入报告任务关联关系时发生异常，报告ID: {}, 任务数: {}",
                     reportRecordId, summaryIds != null ? summaryIds.length : 0, e);
            // 抛出异常，确保事务回滚，保证数据一致性
            throw new ServiceException("插入报告任务关联关系失败: " + e.getMessage());
        }
    }

    /**
     * 验证任务完成状态
     * 检查所有任务是否满足报告生成条件：finish_rate=100 且 task_status=4
     *
     * @param reports 报表记录列表（包含关联的任务信息）
     * @throws ServiceException 如果任何一个任务未完成
     */
    private void validateTaskCompletionStatus(List<FfsafeScanReportRecordWithTaskInfo> reports) throws ServiceException {
        log.debug("开始验证任务完成状态，报表记录数量: {}", reports != null ? reports.size() : 0);

        if (reports == null || reports.isEmpty()) {
            log.warn("漏扫记录为空，无法验证任务完成状态");
            throw new ServiceException("漏扫记录为空");
        }

        for (FfsafeScanReportRecordWithTaskInfo report : reports) {
            // 验证完成率必须为100%
            if (report.getFinishRate() == null || !report.getFinishRate().equals(100)) {
                log.warn("任务未完成！报表记录ID: {}, 完成率: {}%, 要求: 100%",
                        report.getId(), report.getFinishRate());
                throw new ServiceException("任务未完成！");
            }

            // 验证任务状态必须为已完成(4)
            if (report.getTaskStatus() == null || !report.getTaskStatus().equals(4)) {
                log.warn("任务未完成！报表记录ID: {}, 任务状态: {}, 要求: 4(已完成)",
                        report.getId(), report.getTaskStatus());
                throw new ServiceException("任务未完成！");
            }

            log.debug("任务完成状态验证通过 - 报表记录ID: {}, 完成率: {}%, 任务状态: {}",
                    report.getId(), report.getFinishRate(), report.getTaskStatus());
        }

        log.info("所有任务完成状态验证通过，共验证 {} 个任务", reports.size());
    }

}