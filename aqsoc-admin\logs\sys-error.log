2025-08-16 00:00:00.074 [quartzScheduler_Worker-5] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:54)
	... 10 common frames omitted
2025-08-16 00:00:00.083 [quartzScheduler_Worker-4] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:54)
	... 10 common frames omitted
2025-08-16 00:00:00.097 [quartzScheduler_Worker-3] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.HostVulnScan.scan(HostVulnScan.java:61)
	... 10 common frames omitted
2025-08-16 00:00:00.105 [quartzScheduler_Worker-1] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.HostVulnScan.scan(HostVulnScan.java:61)
	... 10 common frames omitted
2025-08-16 00:00:05.013 [taskScheduler-22] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-16 00:17:31.700 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2420 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 00:17:19","/v2/flow-bypass-filtering-log"]
2025-08-16 00:30:05.020 [taskScheduler-3] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-16 00:40:02.220 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2985 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 00:39:49","/v2/flow-bypass-filtering-log"]
2025-08-16 01:02:25.586 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4983 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 01:02:10","/v2/flow-bypass-filtering-log"]
2025-08-16 01:24:53.049 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8475 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 01:24:34","/v2/flow-bypass-filtering-log"]
2025-08-16 02:09:42.581 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3493 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 02:09:29","/v2/flow-bypass-filtering-log"]
2025-08-16 02:32:06.014 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4503 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 02:31:51","/v2/flow-bypass-filtering-log"]
2025-08-16 02:54:28.979 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4527 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 02:54:14","/v2/flow-bypass-filtering-log"]
2025-08-16 03:16:52.980 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5666 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 03:16:37","/v2/flow-bypass-filtering-log"]
2025-08-16 04:01:44.275 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4788 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 04:01:29","/v2/flow-bypass-filtering-log"]
2025-08-16 04:24:08.647 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5318 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 04:23:53","/v2/flow-bypass-filtering-log"]
2025-08-16 04:46:31.607 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4728 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 04:46:16","/v2/flow-bypass-filtering-log"]
2025-08-16 05:53:40.360 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4965 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 05:53:25","/v2/flow-bypass-filtering-log"]
2025-08-16 06:16:06.610 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8050 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 06:15:48","/v2/flow-bypass-filtering-log"]
2025-08-16 07:00:55.070 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2230 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 07:00:42","/v2/flow-bypass-filtering-log"]
2025-08-16 07:45:44.352 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2354 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 07:45:31","/v2/flow-bypass-filtering-log"]
2025-08-16 08:08:08.884 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5422 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 08:07:53","/v2/flow-bypass-filtering-log"]
2025-08-16 08:30:31.433 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4635 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 08:30:16","/v2/flow-bypass-filtering-log"]
2025-08-16 09:15:19.546 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2843 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 09:15:06","/v2/flow-bypass-filtering-log"]
2025-08-16 09:49:02.349 [hutool-cron-1] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 10:31:36.333 [hutool-cron-2] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 10:31:51.309 [hutool-cron-1] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 10:32:49.299 [hutool-cron-2] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 10:44:55.933 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3088 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 10:44:42","/v2/flow-bypass-filtering-log"]
2025-08-16 11:07:20.422 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6268 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 11:07:04","/v2/flow-bypass-filtering-log"]
2025-08-16 11:08:11.328 [hutool-cron-3] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:15:15.330 [hutool-cron-4] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:16:37.296 [hutool-cron-5] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:17:09.301 [hutool-cron-4] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:17:47.319 [hutool-cron-5] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:20:06.288 [hutool-cron-6] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:20:38.330 [hutool-cron-5] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:22:59.252 [hutool-cron-7] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:28:24.212 [hutool-cron-8] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:29:10.364 [hutool-cron-7] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:29:45.621 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6859 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 11:29:28","/v2/flow-bypass-filtering-log"]
2025-08-16 11:30:30.387 [hutool-cron-9] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:39:48.288 [hutool-cron-10] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 11:52:26.282 [hutool-cron-11] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 12:14:38.805 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6229 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 12:14:22","/v2/flow-bypass-filtering-log"]
2025-08-16 13:21:50.081 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5225 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 13:21:34","/v2/flow-bypass-filtering-log"]
2025-08-16 13:44:13.377 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4782 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 13:43:58","/v2/flow-bypass-filtering-log"]
2025-08-16 14:03:33.828 [http-nio-8080-exec-1] ERROR c.r.f.s.e.ScanTaskReport - [createBatchTaskReport,191] - 批量创建报表记录报告业务异常，任务汇总ID: [3], 错误信息: 任务汇总ID 93 已经生成过报表，请勿重复生成!
2025-08-16 14:03:33.900 [http-nio-8080-exec-1] ERROR c.r.f.s.c.FfsafeScantaskSummaryController - [createBatchTaskReport,167] - 批量生成扫描任务报告时发生异常
com.ruoyi.common.exception.ServiceException: 任务汇总ID 93 已经生成过报表，请勿重复生成!
	at com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport.validateNoDuplicateReports(ScanTaskReport.java:216)
	at com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport.createBatchTaskReport(ScanTaskReport.java:128)
	at com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport$$FastClassBySpringCGLIB$$37d220c2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport$$EnhancerBySpringCGLIB$$d4e383d1.createBatchTaskReport(<generated>)
	at com.ruoyi.ffsafe.scantaskapi.controller.FfsafeScantaskSummaryController.createBatchTaskReport(FfsafeScantaskSummaryController.java:157)
	at com.ruoyi.ffsafe.scantaskapi.controller.FfsafeScantaskSummaryController$$FastClassBySpringCGLIB$$bea5a399.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.ffsafe.scantaskapi.controller.FfsafeScantaskSummaryController$$EnhancerBySpringCGLIB$$20a6bca2.createBatchTaskReport(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.ruoyi.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 14:06:36.738 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5265 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 14:06:21","/v2/flow-bypass-filtering-log"]
2025-08-16 14:34:51.241 [pool-7-thread-1] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-16 14:34:56.427 [pool-7-thread-1] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-16 14:34:58.627 [Thread-10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1571 millis. insert into ffsafe_hostscan_vulnresult
         ( task_id,
            host_ip,
            host_port,
            version_info,
            vuln_id,
            vuln_name,
            risk_level,
            cve_number,
            cnnvd_number,
            vuln_type,
            publish_date,
            vuln_info,
            vuln_solve,
            evidence ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ? )[0,"***************",9001,"MinIO Console",50244,"远端WEB服务器上存在/robots.txt文件【原理扫描】",1,"","","其他","2000-01-01 08:00:00","一些WEB服务器通过设置/robots.txt使得一些搜索引擎或者索引工具更方便有效地工作。但/robots.txt中往往存在一些系统信息，可能使攻击者获得该系统的额外信息。","建议您采取以下措施以降低威胁：\r\n\r\n* 如果您的robots.txt中包含敏感信息，删除该文件。","5pS75Ye7UGF5bG9hZDoKR0VUIC9yb2JvdHMudHh0IApIb3N0OiAxOTIuMTY4LjIwMC4xNjg6OTAwMQpVc2VyLUFnZW50OiBNb..."]
2025-08-16 14:35:09.459 [pool-12-thread-4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1046 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) ) and ta.handle_state=0["**************","***************","***************","**************","***************","***************"]
2025-08-16 14:35:09.524 [pool-12-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1085 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-16 14:35:09.619 [pool-12-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1142 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         ) ) and ta.handle_state=0["***************","***************","***************","***************"]
2025-08-16 14:45:00.515 [hutool-cron-2] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 14:45:00.517 [hutool-cron-1] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 14:51:26.893 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1666 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 14:51:15","/v2/flow-bypass-filtering-log"]
2025-08-16 15:09:20.510 [http-nio-8080-exec-23] ERROR c.r.f.s.e.ScanTaskReport - [createBatchTaskReport,191] - 批量创建报表记录报告业务异常，任务汇总ID: [760], 错误信息: 任务汇总ID 724 已经生成过报表，请勿重复生成!
2025-08-16 15:09:20.526 [http-nio-8080-exec-23] ERROR c.r.f.s.c.FfsafeScantaskSummaryController - [createBatchTaskReport,167] - 批量生成扫描任务报告时发生异常
com.ruoyi.common.exception.ServiceException: 任务汇总ID 724 已经生成过报表，请勿重复生成!
	at com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport.validateNoDuplicateReports(ScanTaskReport.java:216)
	at com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport.createBatchTaskReport(ScanTaskReport.java:128)
	at com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport$$FastClassBySpringCGLIB$$37d220c2.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport$$EnhancerBySpringCGLIB$$815b10e.createBatchTaskReport(<generated>)
	at com.ruoyi.ffsafe.scantaskapi.controller.FfsafeScantaskSummaryController.createBatchTaskReport(FfsafeScantaskSummaryController.java:157)
	at com.ruoyi.ffsafe.scantaskapi.controller.FfsafeScantaskSummaryController$$FastClassBySpringCGLIB$$bea5a399.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.ffsafe.scantaskapi.controller.FfsafeScantaskSummaryController$$EnhancerBySpringCGLIB$$405b1755.createBatchTaskReport(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.ruoyi.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 15:10:23.795 [http-nio-8080-exec-39] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - [log,175] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "%2e"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:196)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 15:13:01.673 [http-nio-8080-exec-54] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - [log,175] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "%25"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:196)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 15:13:01.690 [http-nio-8080-exec-55] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - [log,175] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "%25"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:196)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 15:13:01.694 [http-nio-8080-exec-53] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - [log,175] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "%25"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:196)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 15:13:51.591 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3877 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 15:13:37","/v2/flow-bypass-filtering-log"]
2025-08-16 15:58:39.972 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1071 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 15:58:28","/v2/flow-bypass-filtering-log"]
2025-08-16 16:15:36.322 [hutool-cron-3] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 16:57:35.275 [hutool-cron-4] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$startTask$0(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-16 17:05:52.549 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4525 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-16 17:05:37","/v2/flow-bypass-filtering-log"]
